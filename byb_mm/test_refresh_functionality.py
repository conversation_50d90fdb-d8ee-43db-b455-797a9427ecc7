#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 calculate_exposure.py 中的刷新功能
"""

import sys
import time
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_refresh_config():
    """测试刷新配置参数"""
    print("=== 测试刷新配置参数 ===")
    
    try:
        # 导入配置
        sys.path.append('.')
        from calculate_exposure import HEDGE_CONFIG
        
        # 检查刷新相关配置
        refresh_configs = [
            'refresh_interval_hours',
            'refresh_price_deviation_threshold', 
            'max_order_age_hours'
        ]
        
        print("刷新配置参数:")
        for config in refresh_configs:
            if config in HEDGE_CONFIG:
                print(f"  {config}: {HEDGE_CONFIG[config]}")
            else:
                print(f"  ❌ 缺少配置: {config}")
        
        # 计算刷新间隔（秒）
        refresh_interval_seconds = HEDGE_CONFIG.get('refresh_interval_hours', 3) * 3600
        max_age_seconds = HEDGE_CONFIG.get('max_order_age_hours', 6) * 3600
        
        print(f"\n时间配置:")
        print(f"  刷新间隔: {refresh_interval_seconds}秒 ({refresh_interval_seconds/3600}小时)")
        print(f"  最大年龄: {max_age_seconds}秒 ({max_age_seconds/3600}小时)")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_refresh_functions():
    """测试刷新相关函数"""
    print("\n=== 测试刷新函数 ===")
    
    try:
        from calculate_exposure import (
            should_refresh_orders,
            cancel_all_hedge_orders,
            refresh_hedge_orders_based_on_market,
            perform_order_refresh
        )
        
        functions = [
            'should_refresh_orders',
            'cancel_all_hedge_orders', 
            'refresh_hedge_orders_based_on_market',
            'perform_order_refresh'
        ]
        
        print("刷新函数检查:")
        for func_name in functions:
            if func_name in globals() or func_name in locals():
                print(f"  ✅ {func_name}: 已定义")
            else:
                print(f"  ❌ {func_name}: 未找到")
        
        return True
        
    except ImportError as e:
        print(f"❌ 函数导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 函数测试失败: {e}")
        return False


def test_time_tracking():
    """测试时间跟踪功能"""
    print("\n=== 测试时间跟踪 ===")
    
    try:
        from calculate_exposure import last_refresh_time, order_creation_times
        
        print("时间跟踪变量:")
        print(f"  last_refresh_time: {last_refresh_time}")
        print(f"  last_refresh_time (datetime): {datetime.fromtimestamp(last_refresh_time)}")
        print(f"  order_creation_times: {order_creation_times}")
        print(f"  order_creation_times 类型: {type(order_creation_times)}")
        
        # 测试时间计算
        current_time = time.time()
        time_diff = current_time - last_refresh_time
        print(f"  距离上次刷新: {time_diff:.1f}秒 ({time_diff/3600:.2f}小时)")
        
        return True
        
    except Exception as e:
        print(f"❌ 时间跟踪测试失败: {e}")
        return False


def test_refresh_logic():
    """测试刷新逻辑"""
    print("\n=== 测试刷新逻辑 ===")
    
    try:
        from calculate_exposure import should_refresh_orders, HEDGE_CONFIG
        
        # 测试刷新检查
        need_refresh, reason = should_refresh_orders()
        print(f"当前是否需要刷新: {need_refresh}")
        print(f"原因: {reason}")
        
        # 显示配置信息
        refresh_interval = HEDGE_CONFIG.get('refresh_interval_hours', 3)
        price_threshold = HEDGE_CONFIG.get('refresh_price_deviation_threshold', 0.005)
        max_age = HEDGE_CONFIG.get('max_order_age_hours', 6)
        
        print(f"\n刷新配置:")
        print(f"  刷新间隔: {refresh_interval}小时")
        print(f"  价格偏离阈值: {price_threshold:.1%}")
        print(f"  最大订单年龄: {max_age}小时")
        
        return True
        
    except Exception as e:
        print(f"❌ 刷新逻辑测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试 calculate_exposure.py 刷新功能")
    print("=" * 50)
    
    tests = [
        ("配置参数测试", test_refresh_config),
        ("刷新函数测试", test_refresh_functions), 
        ("时间跟踪测试", test_time_tracking),
        ("刷新逻辑测试", test_refresh_logic)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！刷新功能已正确实现。")
    else:
        print("⚠️  部分测试失败，请检查实现。")


if __name__ == '__main__':
    main()
