import sys
sys.path.append("/home/<USER>/byb_mm/")
from byex.spot.market import Spot
from byex.spot.trade import SpotTrade
import random
import time
import pandas as pd
import numpy as np
import config
import con_pri
import logging
import requests
import pytz
from datetime import datetime
import asyncio
import json
import os
import pymysql
import math
from typing import Dict, Optional, Tuple

import threading

beijing_tz = pytz.timezone('Asia/Shanghai')

# df_asks, df_bids = spot_market.get_orderbook(symbol)

# 记录上次检查交易的时间
last_trade_check_time = time.time()

# 共享价格文件路径
SHARED_PRICES_FILE = 'current_mm_prices.json'
# SHARED_TARGET_PRICE_FILE = 'current_target_prices.json'

host = "127.0.0.1"  # 服务器地址
port = 3306  # MySQL 端口
user = "tsen"  # 数据库用户名
password = "12345678"  # 数据库密码

database = "pricing_model"  # 要连接的交易数据库 - 实盘 以及，记录用户交易需要铺出去的单子
database_trade = "market_making_db"  # 铺盘数据 orders_6767088_bybusdt

##################################### - simulation & actual translate - #####################################################

table_eaten_order = 'eating_order' ## 正式服
# table_eaten_order = 'eating_order_simulation' ## 模拟服

spot_market = Spot()
spot_client = SpotTrade(con_pri.api_key, con_pri.api_secret)

symbol = 'bybusdt'
base_url = "https://openapi.100ex.com"## 正式服

# symbol = 'bybusdt'
# base_url = "https://openapi.100exdemo.com" ## 模拟服
spot_market.BASE_URL = base_url
spot_client.BASE_URL = base_url
max_usdt = 200

HEDGE_CONFIG = {
    'max_single_order': 50000,  # 单笔最大对冲数量
    'max_total_hedge': 500000,  # 总对冲数量限制
    'min_spread_ratio': 0.001,  # 最小价差比例（千一）
    'max_price_deviation': 0.005,  # 最大价格偏离（千五）
    'batch_interval': 0.1,  # 分批下单间隔（秒）
    'price_step': 0.0001,  # 价格步长

    # 优化：减少挂单量到10%，增加价格分散度
    'volume_reduction_factor': 0.1,  # 挂单量减少到10%
    'min_batch_size': 1000,  # 最小批次数量
    'max_batches': 8,  # 最大批次数量（从12减少到8，减少订单密集度）
    'price_spread_ratio': 0.008,  # 价格分散范围（从千二增加到千八，增加分散度）
    'quantity_variation': 0.5,  # 数量变异系数（从0.3增加到0.5，增加随机性）
    'time_interval_range': (0.2, 0.8),  # 时间间隔范围（增加间隔，减少密集度）

    # 定期刷新挂单配置
    'refresh_interval_hours': 3,  # 每3小时刷新一次挂单
    'refresh_price_deviation_threshold': 0.005,  # 价格偏离超过千五时立即刷新
    'max_order_age_hours': 6,  # 订单最大存活时间（小时）

    # 伪装策略参数
    'human_like_sizes': [1000, 1500, 2000, 2500, 3000, 5000, 8000, 10000, 15000, 20000, 25000, 30000, 50000],  # 人类常用数量
    'round_number_probability': 0.6,  # 使用整数的概率
    'micro_adjustment_range': 0.00008,  # 微调范围（从0.00002增加到0.00008，增加价格分散）
}

# 全局变量存储当前活跃的对冲单
active_hedge_orders = {
    'buy_hedge': [],   # 对冲卖单的买单列表
    'sell_hedge': []   # 对冲买单的卖单列表
}

# 全局变量跟踪刷新时间
last_refresh_time = time.time()  # 上次刷新时间
order_creation_times = {}  # 记录每个订单的创建时间 {order_id: timestamp}

##########################################################################################

def store_user_trade(dataframe, table_name=table_eaten_order):
    # 数据库连接参数（这些应该从配置文件或环境变量读取）
    conns = pymysql.connect(host=host, port=port, user=user, password=password, database=database)  
    try:
        with conns.cursor() as cursor:
            # 准备插入数据的SQL语句
            insert_sql = f"""
            INSERT INTO {table_name} 
            (price, eat_qty, symbol, order_id, timestamp, trade_direction, status)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
                price = VALUES(price),
                eat_qty = VALUES(eat_qty),
                symbol = VALUES(symbol),
                order_id = VALUES(order_id),
                timestamp = VALUES(timestamp),
                trade_direction = VALUES(trade_direction),
                status = VALUES(status)
            """
            # 将DataFrame转换为元组列表
            records = []
            for _, row in dataframe.iterrows():
                record = (
                    float(row['price']),
                    float(row['eat_qty']),
                    str(row['symbol']),
                    str(row['order_id']),
                    row['timestamp'].strftime('%Y-%m-%d %H:%M:%S') if isinstance(row['timestamp'], datetime) else str(row['timestamp']),
                    str(row['trade_direction']),
                    int(0)
                )
                records.append(record)
            
            # 批量插入数据
            if records:
                cursor.executemany(insert_sql, records)
                conns.commit()
                
                inserted_count = cursor.rowcount
                logging.info(f"成功插入/更新 {inserted_count} 条订单记录到数据库")
                
                # # 可选：查询并验证插入的数据
                # cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE order_id IN ({','.join(['%s'] * len(dataframe))})", 
                #              dataframe['order_id'].tolist())
                # count = cursor.fetchone()[0]
                # logging.info(f"数据库中现有 {count} 条相关订单记录")

                return True
            else:
                logging.warning("没有要插入的记录")
                return True
                
    except pymysql.Error as e:
        logging.error(f"数据库操作失败: {e}")
        conns.rollback()
        return False
        
    except Exception as e:
        logging.error(f"存储订单记录时发生错误: {e}")
        conns.rollback()
        return False
        
    finally:
        conns.close()
        logging.info("数据库连接已关闭")



def generate_random_sleep():
    """生成随机睡眠时间"""
    import random
    return random.uniform(55, 65)  # 55-65秒随机



# 自定义时间格式器，确保日志时间是北京时区时间
class BeijingTimeFormatter(logging.Formatter):
    def converter(self, timestamp):
        # 设置时区为北京时间
        dt = datetime.fromtimestamp(timestamp, beijing_tz)
        return dt.timetuple()

logging.basicConfig(
    # filename='client_push_trading_simu.log',  # Log to this file simulation
    filename='AAA_summary_trade.log',  # Log to this file

    # level=logging.DEBUG,  # Set log level to DEBUG to capture all log messages
    level=logging.INFO,  # set info, just to ignore too many rebundant information
    format='%(asctime)s - %(levelname)s - %(message)s',  # Log format
    datefmt='%Y-%m-%d %H:%M:%S'  # 指定时间格式
)

# 自定义 Formatter 并应用
logger = logging.getLogger()
for handler in logger.handlers:
    handler.setFormatter(BeijingTimeFormatter('%(asctime)s - %(levelname)s - %(message)s'))


# 自定义时间格式器，确保日志时间是北京时区时间
class BeijingTimeFormatter(logging.Formatter):
    def converter(self, timestamp):
        dt = datetime.fromtimestamp(timestamp, beijing_tz)
        return dt.timetuple()

logger = logging.getLogger()
for handler in logger.handlers:
    handler.setFormatter(BeijingTimeFormatter('%(asctime)s - %(levelname)s - %(message)s'))

def aggregate_bybusdt(table_name=table_eaten_order):
    conns = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
    try:
        with conns.cursor() as cursor:
            # 1. 查询bybusdt的所有未处理交易数据
            select_sql = f"""
            SELECT price, eat_qty, trade_direction, timestamp, order_id
            FROM {table_name} 
            WHERE symbol = 'bybusdt' AND id > 2 AND status = 0
            ORDER BY timestamp
            """
            cursor.execute(select_sql)
            trades = cursor.fetchall()
            if not trades:
                logging.info("没有找到 bybusdt 的未处理交易数据")
                # 不直接返回，而是返回一个标识，表示没有新交易但需要继续处理数据库中的聚合数据
                return {'no_new_trades': True, 'processed_trades_count': 0}

            # 2. 转换为DataFrame进行计算
            df = pd.DataFrame(trades, columns=['price', 'eat_qty', 'trade_direction', 'timestamp', 'order_id'])
            df['price'] = df['price'].astype(float)
            df['eat_qty'] = df['eat_qty'].astype(float)
    
            # 3. 分别计算买单和卖单
            buy_trades = df[df['trade_direction'] == 'buy']
            sell_trades = df[df['trade_direction'] == 'sell']
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 初始化返回结果
            result = {
                'buy_order': None,
                'sell_order': None,
                'processed_trades_count': len(trades)
            }

            # 4. 处理买单汇总
            if not buy_trades.empty:
                buy_total_value = (buy_trades['price'] * buy_trades['eat_qty']).sum()
                buy_total_qty = buy_trades['eat_qty'].sum()
                buy_new_avg_price = buy_total_value / buy_total_qty
                buy_latest_timestamp = buy_trades['timestamp'].max()
                buy_latest_order_id = buy_trades['order_id'].iloc[-1]
                
                # 先查询id=1是否已存在数据
                cursor.execute(f"SELECT price, eat_qty FROM {table_name} WHERE id = 1 AND symbol = 'bybusdt'")
                existing_buy = cursor.fetchone()
                
                if existing_buy:
                    # 如果已存在，计算新的加权平均价格
                    existing_price, existing_qty = existing_buy
                    existing_value = existing_price * existing_qty
                    
                    # 新的总价值 = 现有价值 + 新增价值
                    total_value = existing_value + buy_total_value
                    # 新的总数量 = 现有数量 + 新增数量
                    total_qty = existing_qty + buy_total_qty
                    # 新的加权平均价格
                    final_avg_price = total_value / total_qty
                    
                    logging.info(f"买单更新 - 现有: {existing_qty}@{existing_price:.6f}, 新增: {buy_total_qty}@{buy_new_avg_price:.6f}")
                    logging.info(f"买单最终 - 总量: {total_qty:.2f}, 平均价格: {final_avg_price:.6f}")
                else:
                    # 如果不存在，直接使用新数据
                    total_qty = buy_total_qty
                    final_avg_price = buy_new_avg_price
                    logging.info(f"买单新建 - 数量: {total_qty:.2f}, 平均价格: {final_avg_price:.6f}")
                
                upsert_buy_sql = f"""
                INSERT INTO {table_name} 
                (id, price, eat_qty, symbol, order_id, timestamp, trade_direction, created_at, status)
                VALUES (1, %s, %s, 'bybusdt', %s, %s, 'buy', %s, 0)
                ON DUPLICATE KEY UPDATE
                    price = VALUES(price),
                    eat_qty = VALUES(eat_qty),
                    order_id = VALUES(order_id),
                    timestamp = VALUES(timestamp),
                    created_at = VALUES(created_at)
                """
                
                cursor.execute(upsert_buy_sql, (
                    round(final_avg_price, 6),
                    round(total_qty, 2),
                    str(buy_latest_order_id),
                    buy_latest_timestamp,
                    current_time
                ))
                
                # 保存买单信息到返回结果
                result['buy_order'] = {
                    'id': 1,
                    'symbol': 'bybusdt',
                    'trade_direction': 'buy',
                    'price': round(final_avg_price, 6),
                    'eat_qty': round(total_qty, 2),
                    'order_id': str(buy_latest_order_id),
                    'timestamp': buy_latest_timestamp,
                    'created_at': current_time,
                    # 'status': 0,
                    'total_value': round(final_avg_price * total_qty, 2),
                    # 'new_trades_count': len(buy_trades),
                    # 'new_qty_added': round(buy_total_qty, 2),
                    # 'existing_qty': round(existing_buy[1], 2) if existing_buy else 0,
                    # 'existing_price': round(existing_buy[0], 6) if existing_buy else 0
                }

            # 5. 处理卖单汇总
            if not sell_trades.empty:
                sell_total_value = (sell_trades['price'] * sell_trades['eat_qty']).sum()
                sell_total_qty = sell_trades['eat_qty'].sum()
                sell_new_avg_price = sell_total_value / sell_total_qty
                sell_latest_timestamp = sell_trades['timestamp'].max()
                sell_latest_order_id = sell_trades['order_id'].iloc[-1]
                
                # 先查询id=2是否已存在数据
                cursor.execute(f"SELECT price, eat_qty FROM {table_name} WHERE id = 2 AND symbol = 'bybusdt'")
                existing_sell = cursor.fetchone()
                
                if existing_sell:
                    # 如果已存在，计算新的加权平均价格
                    existing_price, existing_qty = existing_sell
                    existing_value = existing_price * existing_qty
                    
                    # 新的总价值 = 现有价值 + 新增价值
                    total_value = existing_value + sell_total_value
                    # 新的总数量 = 现有数量 + 新增数量
                    total_qty = existing_qty + sell_total_qty
                    # 新的加权平均价格
                    final_avg_price = total_value / total_qty
                    
                    logging.info(f"卖单更新 - 现有: {existing_qty}@{existing_price:.6f}, 新增: {sell_total_qty}@{sell_new_avg_price:.6f}")
                    logging.info(f"卖单最终 - 总量: {total_qty:.2f}, 平均价格: {final_avg_price:.6f}")
                else:
                    # 如果不存在，直接使用新数据
                    total_qty = sell_total_qty
                    final_avg_price = sell_new_avg_price
                    logging.info(f"卖单新建 - 数量: {total_qty:.2f}, 平均价格: {final_avg_price:.6f}")

                upsert_sell_sql = f"""
                INSERT INTO {table_name} 
                (id, price, eat_qty, symbol, order_id, timestamp, trade_direction, created_at, status)
                VALUES (2, %s, %s, 'bybusdt', %s, %s, 'sell', %s, 0)
                ON DUPLICATE KEY UPDATE
                    price = VALUES(price),
                    eat_qty = VALUES(eat_qty),
                    order_id = VALUES(order_id),
                    timestamp = VALUES(timestamp),
                    created_at = VALUES(created_at)
                """
                
                cursor.execute(upsert_sell_sql, (
                    round(final_avg_price, 6),
                    round(total_qty, 2),
                    str(sell_latest_order_id),
                    sell_latest_timestamp,
                    current_time
                ))
                
                # 保存卖单信息到返回结果
                result['sell_order'] = {
                    'id': 2,
                    'symbol': 'bybusdt',
                    'trade_direction': 'sell',
                    'price': round(final_avg_price, 6),
                    'eat_qty': round(total_qty, 2),
                    'order_id': str(sell_latest_order_id),
                    'timestamp': sell_latest_timestamp,
                    'created_at': current_time,
                    # 'status': 0,
                    'total_value': round(final_avg_price * total_qty, 2),
                    # 'new_trades_count': len(sell_trades),
                    # 'new_qty_added': round(sell_total_qty, 2),
                    # 'existing_qty': round(existing_sell[1], 2) if existing_sell else 0,
                    # 'existing_price': round(existing_sell[0], 6) if existing_sell else 0
                }

            # 6. 批量更新所有处理过的交易状态为1
            if trades:
                trade_ids = [str(trade[4]) for trade in trades]
                placeholders = ','.join(['%s'] * len(trade_ids))
                
                update_status_sql = f"""
                UPDATE {table_name} 
                SET status = 1 
                WHERE symbol = 'bybusdt' AND order_id IN ({placeholders}) AND id > 2
                """
                
                cursor.execute(update_status_sql, trade_ids)
                updated_count = cursor.rowcount
                logging.info(f"已将 {updated_count} 条交易记录的status更新为1")
            
            # 7. 一次性提交所有更改
            conns.commit()
            
            logging.info(f"成功聚合 bybusdt 的 {len(trades)} 条交易数据")
            return result
            
    except Exception as e:
        logging.error(f"聚合bybusdt交易数据时发生错误: {e}")
        conns.rollback()
        return None
        
    finally:
        conns.close()
        logging.info("数据库连接已关闭")



def get_current_aggregated_orders(table_name=table_eaten_order):
    """获取当前id=1和id=2的聚合订单"""
    conns = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
    try:
        with conns.cursor() as cursor:
            # 查询id=1和id=2的聚合订单
            select_sql = f"""
            SELECT id, price, eat_qty, trade_direction, order_id, timestamp
            FROM {table_name} 
            WHERE symbol = 'bybusdt' AND id IN (1, 2)
            ORDER BY id
            """
            cursor.execute(select_sql)
            orders = cursor.fetchall()
            
            result = {'buy_order': None, 'sell_order': None}
            
            for order in orders:
                order_id, price, eat_qty, trade_direction, original_order_id, timestamp = order
                order_data = {
                    'id': order_id,
                    'price': float(price),
                    'eat_qty': float(eat_qty),
                    'trade_direction': trade_direction,
                    'order_id': original_order_id,
                    'timestamp': timestamp,
                    'total_value': float(price) * float(eat_qty)
                }
                
                if order_id == 1:  # 买单汇总
                    result['buy_order'] = order_data
                elif order_id == 2:  # 卖单汇总
                    result['sell_order'] = order_data
            
            return result
    except Exception as e:
        logging.error(f"获取聚合订单时发生错误: {e}")
        return {'buy_order': None, 'sell_order': None}
    finally:
        conns.close()


def get_current_market_price():
    """获取当前市场价格"""
    try:
        ticker = spot_market.get_ticker(symbol='bybusdt')
        if not ticker.empty:
            return float(ticker['last'])
        return None
    except Exception as e:
        logging.error(f"获取市场价格失败: {e}")
        return None


def get_orderbook_info():
    """获取当前盘口信息"""
    try:
        asks_df, bids_df = spot_market.get_orderbook('bybusdt')
        if not asks_df.empty and not bids_df.empty:
            best_bid = float(bids_df['bids_price'].iloc[0])
            best_ask = float(asks_df['asks_price'].iloc[0])
            spread = best_ask - best_bid
            spread_ratio = spread / best_bid
            return {
                'best_bid': best_bid,
                'best_ask': best_ask,
                'spread': spread,
                'spread_ratio': spread_ratio
            }
        return None
    except Exception as e:
        logging.error(f"获取盘口信息失败: {e}")
        return None
    

def calculate_hedge_price(base_price: float, is_buy_hedge: bool, quantity: float = 0) -> float:
    """
    动态计算对冲价格，考虑市场情况和数量

    Parameters:
    -----------
    base_price : float
        基础价格（聚合订单的价格）
    is_buy_hedge : bool
        是否是买单对冲（对冲卖单用买单）
    quantity : float
        对冲数量，用于调整价格偏移

    Returns:
    --------
    float: 对冲价格
    """
    # 获取当前市场信息
    market_price = get_current_market_price()
    orderbook_info = get_orderbook_info()

    # 基础对冲比例
    base_hedge_ratio = 0.998

    # 根据市场情况调整对冲比例
    if orderbook_info:
        spread_ratio = orderbook_info['spread_ratio']

        # 如果价差较大，可以更激进一些
        if spread_ratio > 0.002:  # 价差超过千二
            base_hedge_ratio = 0.9985  # 更接近市场价
        elif spread_ratio < 0.0012:  # 价差很小
            base_hedge_ratio = 0.995  # 更保守

    # 根据数量调整价格偏移（大单更保守）
    quantity_factor = 1.0
    if quantity > 50000:  # 大单
        quantity_factor = 0.998
    elif quantity > 100000:  # 超大单
        quantity_factor = 0.995

    # 最终对冲比例
    final_hedge_ratio = base_hedge_ratio * quantity_factor

    if is_buy_hedge:
        # 对冲卖单：在卖价下方挂买单
        hedge_price = base_price * final_hedge_ratio

        # 确保不超过当前最佳买价
        if orderbook_info and hedge_price >= orderbook_info['best_bid']:
            hedge_price = orderbook_info['best_bid'] * 0.9999

    else:
        # 对冲买单：在买价上方挂卖单
        hedge_price = base_price / final_hedge_ratio

        # 确保不低于当前最佳卖价
        if orderbook_info and hedge_price <= orderbook_info['best_ask']:
            hedge_price = orderbook_info['best_ask'] * 1.0001

    return round(hedge_price, 4)


def generate_human_like_quantity(target_size: float) -> float:
    """
    生成类似人类交易者的数量

    Parameters:
    -----------
    target_size : float
        目标数量

    Returns:
    --------
    float: 调整后的数量
    """
    import random

    # 如果目标数量很小，直接返回
    if target_size < 1000:
        return target_size

    # 有一定概率使用人类常用的整数数量
    if random.random() < HEDGE_CONFIG['round_number_probability']:
        human_sizes = HEDGE_CONFIG['human_like_sizes']

        # 找到最接近的人类常用数量
        closest_size = min(human_sizes, key=lambda x: abs(x - target_size))

        # 如果差距不大，使用人类常用数量
        if abs(closest_size - target_size) / target_size < 0.3:
            return float(closest_size)

    # 否则对目标数量进行微调，使其看起来更自然
    # 倾向于整百、整千的数量
    if target_size > 10000:
        # 大数量倾向于整千
        rounded = round(target_size / 1000) * 1000
        variation = random.uniform(-500, 500)
        return max(1000, rounded + variation)
    elif target_size > 5000:
        # 中等数量倾向于整百
        rounded = round(target_size / 500) * 500
        variation = random.uniform(-200, 200)
        return max(1000, rounded + variation)
    else:
        # 小数量保持原样，但添加小幅变动
        variation = random.uniform(-100, 100)
        return max(1000, target_size + variation)



def calculate_batch_sizes(total_quantity: float, max_single_order: float = None) -> list:
    """
    计算分批下单的数量分配（随机化处理）

    Parameters:
    -----------
    total_quantity : float
        总数量
    max_single_order : float
        单笔最大数量

    Returns:
    --------
    list: 分批数量列表
    """
    import random

    if max_single_order is None:
        max_single_order = HEDGE_CONFIG['max_single_order']

    if total_quantity <= max_single_order:
        return [total_quantity]

    # 随机化批次数量（在合理范围内）
    # 优化：减少批次数量，降低订单密集度
    min_batches = math.ceil(total_quantity / max_single_order)
    max_batches = min(min_batches + 2, HEDGE_CONFIG['max_batches'])  # 使用配置的最大批次数（8批）
    num_batches = random.randint(min_batches, max_batches)

    # 生成随机权重
    weights = []
    for i in range(num_batches):
        # 使用正态分布生成权重，让数量分布更自然
        weight = random.normalvariate(1.0, 0.3)  # 均值1.0，标准差0.3
        weight = max(0.3, min(2.0, weight))  # 限制在0.3-2.0之间
        weights.append(weight)

    # 归一化权重
    total_weight = sum(weights)
    normalized_weights = [w / total_weight for w in weights]

    # 根据权重分配数量
    batches = []
    remaining = total_quantity

    for i in range(num_batches - 1):
        batch_size = total_quantity * normalized_weights[i]
        # 添加小幅随机波动
        variation = batch_size * random.uniform(-0.1, 0.1)
        batch_size += variation

        # 确保数量合理
        batch_size = max(1000, min(max_single_order, batch_size))

        # 使用人类化数量生成
        batch_size = generate_human_like_quantity(batch_size)

        batches.append(batch_size)
        remaining -= batch_size

    # 最后一批包含剩余数量（确保总数准确）
    remaining = max(1000, remaining)
    remaining = generate_human_like_quantity(remaining)
    batches.append(remaining)

    # 随机打乱顺序，避免大小单有规律
    random.shuffle(batches)

    return batches



def validate_order_params(order_type: str, price: float, quantity: float) -> tuple[bool, str]:
    """
    验证订单参数

    Returns:
    --------
    tuple: (是否有效, 错误信息)
    """
    # 检查订单类型
    if order_type not in ['BUY', 'SELL']:
        return False, f"无效的订单类型: {order_type}"

    # 检查价格
    if price <= 0:
        return False, f"价格必须大于0: {price}"

    if price > 10:  # BYB价格不太可能超过10
        return False, f"价格异常过高: {price}"

    # 检查价格精度（4位小数）
    if len(str(price).split('.')[-1]) > 4:
        return False, f"价格精度超过4位小数: {price}"

    # 检查数量
    if quantity <= 0:
        return False, f"数量必须大于0: {quantity}"

    if quantity < 100:  # 最小交易数量通常是100
        return False, f"数量低于最小限制: {quantity}"

    if quantity > 1000000:  # 单笔不应该超过100万
        return False, f"数量异常过大: {quantity}"

    return True, ""



def place_hedge_order(order_type: str, price: float, quantity: float, max_retries: int = 3) -> Optional[str]:
    """
    下对冲单(改进版,带重试机制)
    
    Parameters:
    -----------
    order_type : str
        订单类型 'BUY' 或 'SELL'
    price : float
        价格
    quantity : float
        数量
    max_retries : int
        最大重试次数
    
    Returns:
    --------
    str: 订单ID,失败返回None
    """
    import time
    
    # 参数验证
    is_valid, error_msg = validate_order_params(order_type, price, quantity)
    if not is_valid:
        logging.error(f"订单参数无效: {error_msg}")
        return None
    
    # 格式化参数
    formatted_price = f"{price:.4f}"
    formatted_quantity = f"{quantity:.2f}"
    
    for attempt in range(max_retries):
        try:
            # 添加重试间隔
            if attempt > 0:
                retry_delay = 0.5 + attempt * 0.2  # 递增延迟
                logging.info(f"第{attempt + 1}次重试下单,等待{retry_delay}秒...")
                time.sleep(retry_delay)
            
            # 调用API
            result = spot_client.new_order(
                symbol='bybusdt',
                side=order_type,
                type=1,  # 限价单
                volume=formatted_quantity,
                price=formatted_price
            )
            
            # 检查结果
            if result and 'order_id' in result:
                order_id = result['order_id']
                logging.info(
                    f"对冲单下单成功: {order_type} {formatted_quantity} @ {formatted_price}, 订单ID: {order_id}")
                return order_id
            else:
                error_detail = ""
                if result:
                    if isinstance(result, dict):
                        error_detail = f", 错误详情: {result}"
                    else:
                        error_detail = f", 返回值: {result}"
                
                logging.warning(f"第{attempt + 1}次下单失败{error_detail}")
                
                # 如果是最后一次尝试,记录错误
                if attempt == max_retries - 1:
                    logging.error(
                        f"对冲单下单最终失败: {order_type} {formatted_quantity} @ {formatted_price}{error_detail}")
        
        except Exception as e:
            logging.warning(f"第{attempt + 1}次下单异常: {e}")
            
            # 如果是最后一次尝试,记录错误
            if attempt == max_retries - 1:
                logging.error(f"下对冲单时发生错误: {e}")
    
    return None



def place_batch_hedge_orders(order_type: str, base_price: float, total_quantity: float, is_buy_hedge: bool) -> list:
    """
    分批下对冲单（随机化价格和时间）

    Parameters:
    -----------
    order_type : str
        订单类型 'BUY' 或 'SELL'
    base_price : float
        基础价格
    total_quantity : float
        总数量
    is_buy_hedge : bool
        是否是买单对冲

    Returns:
    --------
    list: 成功下单的订单ID列表
    """
    import random
    import time

    # 计算分批数量
    batch_sizes = calculate_batch_sizes(total_quantity)
    order_ids = []

    # 生成随机价格偏移范围
    base_hedge_price = calculate_hedge_price(base_price, is_buy_hedge, total_quantity)

    # 根据方向确定价格范围
    # 优化：扩大价格分散范围，减少订单密集度
    spread_factor = HEDGE_CONFIG['price_spread_ratio']  # 使用配置的千八价差
    if is_buy_hedge:
        # 买单对冲，在基础价格下方分布，扩大分散范围
        price_range_start = base_hedge_price * (1 - spread_factor * 0.8)  # 下方约6.4‰
        price_range_end = base_hedge_price * (1 + spread_factor * 0.2)  # 上方约1.6‰
    else:
        # 卖单对冲，在基础价格上方分布，扩大分散范围
        price_range_start = base_hedge_price * (1 - spread_factor * 0.2)  # 下方约1.6‰
        price_range_end = base_hedge_price * (1 + spread_factor * 0.8)  # 上方约6.4‰

    # 为每批生成随机价格
    used_prices = set()

    for i, batch_size in enumerate(batch_sizes):
        # 生成随机价格，避免重复
        attempts = 0
        while attempts < 20:  # 最多尝试20次
            if is_buy_hedge:
                # 买单对冲：较大的单子价格稍低（更容易成交）
                size_factor = min(1.0, batch_size / 30000)  # 30000以上的单子算大单
                price_bias = -size_factor * 0.0002  # 大单价格偏低
            else:
                # 卖单对冲：较大的单子价格稍高
                size_factor = min(1.0, batch_size / 30000)
                price_bias = size_factor * 0.0002  # 大单价格偏高

            # 在范围内随机选择价格
            random_price = random.uniform(price_range_start, price_range_end) + price_bias

            # 优化：增加更大的随机波动，提高价格分散度
            micro_variation = random.uniform(-HEDGE_CONFIG['micro_adjustment_range'], HEDGE_CONFIG['micro_adjustment_range'])
            random_price += micro_variation

            hedge_price = round(random_price, 4)

            # 检查价格是否已使用（避免完全相同的价格）
            if hedge_price not in used_prices:
                used_prices.add(hedge_price)
                break

            attempts += 1

        # 如果尝试多次仍重复，则使用微调价格
        if attempts >= 20:
            hedge_price = round(random_price + random.uniform(-0.0001, 0.0001), 4)

        # 下单（带重试）
        order_id = place_hedge_order(order_type, hedge_price, batch_size, max_retries=2)
        if order_id:
            order_ids.append({
                'order_id': order_id,
                'price': hedge_price,
                'quantity': batch_size,
                'batch_index': i
            })

            # 成功后短暂等待，避免API限制
            # 优化：使用配置的时间间隔，增加分散度
            if i < len(batch_sizes) - 1:
                success_interval = random.uniform(*HEDGE_CONFIG['time_interval_range'])  # 使用配置的0.2-0.8秒间隔
                time.sleep(success_interval)
        else:
            logging.error(f"第{i + 1}批对冲单下单失败: {order_type} {batch_size} @ {hedge_price}")

            # 失败后等待更长时间再继续
            # 优化：失败后使用更长的间隔，进一步减少密集度
            if i < len(batch_sizes) - 1:
                failure_interval = random.uniform(1.0, 2.0)  # 失败后更长间隔（1-2秒）
                logging.info(f"下单失败，等待{failure_interval:.1f}秒后继续...")
                time.sleep(failure_interval)

    # 统计结果
    success_count = len(order_ids)
    failure_count = len(batch_sizes) - success_count
    success_quantity = sum(order['quantity'] for order in order_ids)
    failure_quantity = total_quantity - success_quantity

    logging.info(f"分批下单完成: 总数量{total_quantity}, 分{len(batch_sizes)}批")
    logging.info(f"  成功: {success_count}笔, 数量: {success_quantity}")
    if failure_count > 0:
        logging.warning(f"  失败: {failure_count}笔, 数量: {failure_quantity}")
        logging.warning(f"  成功率: {success_count / len(batch_sizes) * 100:.1f}%")

    # 按价格排序显示（便于查看分布）
    if order_ids:
        sorted_orders = sorted(order_ids, key=lambda x: x['price'])
        for order in sorted_orders:
            logging.info(f"  对冲单: {order['quantity']} @ {order['price']} (ID: {order['order_id']})")

    # 如果失败率过高，记录警告
    if failure_count > 0 and failure_count / len(batch_sizes) > 0.3:
        logging.warning(f"⚠️  对冲单失败率过高 ({failure_count}/{len(batch_sizes)})，建议检查:")
        logging.warning(f"   1. 账户余额是否充足")
        logging.warning(f"   2. API调用频率是否过高")
        logging.warning(f"   3. 价格是否在合理范围内")
        logging.warning(f"   4. 网络连接是否稳定")

    return order_ids





def get_order_status(order_id: str) -> Tuple[str, float]:
    """
    获取订单状态和已成交数量
    
    Returns:
    --------
    tuple: (状态, 已成交数量)
    """
    try:
        # 根据做市代码，使用get_open_orders来检查订单状态
        open_orders = spot_client.get_open_orders('bybusdt', pageSize=1000)
        
        if open_orders and 'resultList' in open_orders:
            orders = open_orders['resultList']
            
            # 查找对应的订单ID
            for order in orders:
                if str(order.get('id')) == str(order_id):
                    status = order.get('status', 'UNKNOWN')
                    # 计算已成交数量 = 总数量 - 剩余数量
                    orig_qty = float(order.get('volume', 0))
                    remaining_qty = float(order.get('remain_volume', 0))
                    filled_qty = orig_qty - remaining_qty
                    
                    # 状态映射：0=初始, 1=新订单, 2=完全成交, 3=部分成交, 4=已取消, 5=已拒绝
                    status_map = {
                        0: 'NEW',
                        1: 'NEW', 
                        2: 'FILLED',
                        3: 'PARTIALLY_FILLED',
                        4: 'CANCELED',
                        5: 'REJECTED'
                    }
                    
                    status_str = status_map.get(status, 'UNKNOWN')
                    return status_str, filled_qty
            
            # 如果在开放订单中没找到，可能已经完全成交或取消
            return 'FILLED_OR_CANCELED', 0.0
        else:
            return 'UNKNOWN', 0.0
            
    except Exception as e:
        logging.error(f"获取订单状态时发生错误: {e}")
        return 'ERROR', 0.0





def update_aggregated_order_qty(order_id: int, new_qty: float, table_name=table_eaten_order):
    """
    更新聚合订单的数量
    
    Parameters:
    -----------
    order_id : int
        订单ID (1或2)
    new_qty : float
        新的数量
    """
    conns = pymysql.connect(host=host, port=port, user=user, password=password, database=database)
    try:
        with conns.cursor() as cursor:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            update_sql = f"""
            UPDATE {table_name} 
            SET eat_qty = %s, created_at = %s
            WHERE id = %s AND symbol = 'bybusdt'
            """
            
            cursor.execute(update_sql, (new_qty, current_time, order_id))
            conns.commit()
            
            logging.info(f"已更新聚合订单 ID={order_id} 的数量为: {new_qty}")
            
    except Exception as e:
        logging.error(f"更新聚合订单数量时发生错误: {e}")
        conns.rollback()
    finally:
        conns.close()




def cancel_order(order_id: str) -> bool:
    """取消订单"""
    try:
        result = spot_client.cancel_order('bybusdt', order_id)
        logging.info(f"订单 {order_id} 取消结果: {result}")
        return True
    except Exception as e:
        logging.error(f"取消订单 {order_id} 时发生错误: {e}")
        return False


def should_refresh_orders() -> tuple[bool, str]:
    """
    检查是否需要刷新挂单

    Returns:
    --------
    tuple: (是否需要刷新, 刷新原因)
    """
    global last_refresh_time
    current_time = time.time()

    # 检查时间间隔
    time_since_last_refresh = current_time - last_refresh_time
    refresh_interval_seconds = HEDGE_CONFIG['refresh_interval_hours'] * 3600

    if time_since_last_refresh >= refresh_interval_seconds:
        return True, f"定期刷新：距离上次刷新已过{time_since_last_refresh/3600:.1f}小时"

    # 检查价格偏离
    try:
        current_market_price = get_current_market_price()
        if current_market_price:
            # 检查现有对冲单的价格是否偏离市场价格过多
            threshold = HEDGE_CONFIG['refresh_price_deviation_threshold']

            for hedge_type, hedge_orders in active_hedge_orders.items():
                for hedge_info in hedge_orders:
                    order_price = hedge_info['price']
                    price_deviation = abs(order_price - current_market_price) / current_market_price

                    if price_deviation > threshold:
                        return True, f"价格偏离过大：订单价格{order_price}，市场价格{current_market_price}，偏离{price_deviation:.1%}"
    except Exception as e:
        logging.warning(f"检查价格偏离时发生错误: {e}")

    # 检查订单年龄
    max_age_seconds = HEDGE_CONFIG['max_order_age_hours'] * 3600
    for order_id, creation_time in order_creation_times.items():
        order_age = current_time - creation_time
        if order_age > max_age_seconds:
            return True, f"订单过期：订单{order_id}已存在{order_age/3600:.1f}小时"

    return False, "无需刷新"


def cancel_all_hedge_orders(reason: str = "刷新挂单"):
    """
    取消所有对冲单

    Parameters:
    -----------
    reason : str
        取消原因
    """
    global active_hedge_orders, order_creation_times

    logging.info(f"开始取消所有对冲单，原因：{reason}")

    total_cancelled = 0

    # 取消买单对冲
    for hedge_info in active_hedge_orders['buy_hedge']:
        if cancel_order(hedge_info['order_id']):
            total_cancelled += 1
            # 从创建时间记录中移除
            order_creation_times.pop(hedge_info['order_id'], None)

    # 取消卖单对冲
    for hedge_info in active_hedge_orders['sell_hedge']:
        if cancel_order(hedge_info['order_id']):
            total_cancelled += 1
            # 从创建时间记录中移除
            order_creation_times.pop(hedge_info['order_id'], None)

    # 清空对冲单列表
    active_hedge_orders['buy_hedge'] = []
    active_hedge_orders['sell_hedge'] = []

    logging.info(f"已取消{total_cancelled}个对冲单")
    return total_cancelled


def refresh_hedge_orders_based_on_market():
    """
    基于当前市场价格重新创建对冲单
    """
    global last_refresh_time

    logging.info("开始基于市场价格刷新对冲单")

    try:
        # 获取当前聚合订单
        current_orders = get_current_aggregated_orders()
        buy_order = current_orders.get('buy_order')
        sell_order = current_orders.get('sell_order')

        if not buy_order and not sell_order:
            logging.info("没有需要对冲的聚合订单")
            return

        # 获取当前市场价格，用于重新计算对冲价格
        current_market_price = get_current_market_price()
        if not current_market_price:
            logging.warning("无法获取当前市场价格，使用聚合订单价格")

        # 重新创建买单对冲
        if buy_order and buy_order['eat_qty'] > 0:
            target_hedge_qty = buy_order['eat_qty'] * HEDGE_CONFIG['volume_reduction_factor']
            if target_hedge_qty > 0.01:
                # 使用当前市场价格或聚合订单价格
                base_price = current_market_price if current_market_price else buy_order['price']
                logging.info(f"重新创建买单对冲：数量{target_hedge_qty}，基准价格{base_price}")

                new_orders = place_batch_hedge_orders('SELL', base_price, target_hedge_qty, is_buy_hedge=False)

                for order_info in new_orders:
                    active_hedge_orders['sell_hedge'].append({
                        'order_id': order_info['order_id'],
                        'original_qty': order_info['quantity'],
                        'remaining_qty': order_info['quantity'],
                        'price': order_info['price'],
                        'hedge_target_id': 1,
                        'batch_index': order_info['batch_index']
                    })
                    # 记录订单创建时间
                    order_creation_times[order_info['order_id']] = time.time()

        # 重新创建卖单对冲
        if sell_order and sell_order['eat_qty'] > 0:
            target_hedge_qty = sell_order['eat_qty'] * HEDGE_CONFIG['volume_reduction_factor']
            if target_hedge_qty > 0.01:
                # 使用当前市场价格或聚合订单价格
                base_price = current_market_price if current_market_price else sell_order['price']
                logging.info(f"重新创建卖单对冲：数量{target_hedge_qty}，基准价格{base_price}")

                new_orders = place_batch_hedge_orders('BUY', base_price, target_hedge_qty, is_buy_hedge=True)

                for order_info in new_orders:
                    active_hedge_orders['buy_hedge'].append({
                        'order_id': order_info['order_id'],
                        'original_qty': order_info['quantity'],
                        'remaining_qty': order_info['quantity'],
                        'price': order_info['price'],
                        'hedge_target_id': 2,
                        'batch_index': order_info['batch_index']
                    })
                    # 记录订单创建时间
                    order_creation_times[order_info['order_id']] = time.time()

        # 更新刷新时间
        last_refresh_time = time.time()
        logging.info("对冲单刷新完成")

    except Exception as e:
        logging.error(f"刷新对冲单时发生错误: {e}")


def perform_order_refresh():
    """
    执行订单刷新流程
    """
    need_refresh, reason = should_refresh_orders()

    if need_refresh:
        logging.info(f"触发订单刷新：{reason}")

        # 1. 取消所有现有对冲单
        cancelled_count = cancel_all_hedge_orders(reason)

        # 2. 等待一小段时间确保取消完成
        time.sleep(2)

        # 3. 基于当前市场价格重新创建对冲单
        refresh_hedge_orders_based_on_market()

        return True

    return False



def check_and_place_hedge_orders(aggregated_result):
    """
    检查并下对冲单（支持分批）

    Parameters:
    -----------
    aggregated_result : dict
        aggregate_bybusdt() 的返回结果
    """
    global active_hedge_orders

    # 获取当前数据库中的真实聚合数据
    current_orders = get_current_aggregated_orders()
    buy_order = current_orders.get('buy_order')
    sell_order = current_orders.get('sell_order')

    logging.info(
        f"检查是否需要下对冲单 - 买单: {buy_order['eat_qty'] if buy_order else 0}, 卖单: {sell_order['eat_qty'] if sell_order else 0}")

    # 处理买单对冲（买单用卖单对冲）
    if buy_order and buy_order['eat_qty'] > 0:
        # 计算当前已有的对冲数量
        current_hedge_qty = sum(order['remaining_qty'] for order in active_hedge_orders['sell_hedge'])
        # 优化：应用10%减少因子，减少对冲挂单量
        target_hedge_qty = buy_order['eat_qty'] * HEDGE_CONFIG['volume_reduction_factor']
        needed_hedge_qty = target_hedge_qty - current_hedge_qty

        if needed_hedge_qty > 0.01:  # 需要额外的对冲单
            logging.info(f"需要为买单增加卖单对冲: {needed_hedge_qty} (原始量: {buy_order['eat_qty']}, 减少到: {target_hedge_qty})")

            # 分批下对冲单
            new_orders = place_batch_hedge_orders('SELL', buy_order['price'], needed_hedge_qty, is_buy_hedge=False)

            for order_info in new_orders:
                active_hedge_orders['sell_hedge'].append({
                    'order_id': order_info['order_id'],
                    'original_qty': order_info['quantity'],
                    'remaining_qty': order_info['quantity'],
                    'price': order_info['price'],
                    'hedge_target_id': 1,  # 对冲的是id=1的买单
                    'batch_index': order_info['batch_index']
                })
                # 记录订单创建时间
                order_creation_times[order_info['order_id']] = time.time()

            logging.info(f"为买单汇总下了{len(new_orders)}笔卖单对冲，总数量: {needed_hedge_qty}")

    # 处理卖单对冲（卖单用买单对冲）
    if sell_order and sell_order['eat_qty'] > 0:
        # 计算当前已有的对冲数量
        current_hedge_qty = sum(order['remaining_qty'] for order in active_hedge_orders['buy_hedge'])
        # 优化：应用10%减少因子，减少对冲挂单量
        target_hedge_qty = sell_order['eat_qty'] * HEDGE_CONFIG['volume_reduction_factor']
        needed_hedge_qty = target_hedge_qty - current_hedge_qty

        if needed_hedge_qty > 0.01:  # 需要额外的对冲单
            logging.info(f"需要为卖单增加买单对冲: {needed_hedge_qty} (原始量: {sell_order['eat_qty']}, 减少到: {target_hedge_qty})")

            # 分批下对冲单
            new_orders = place_batch_hedge_orders('BUY', sell_order['price'], needed_hedge_qty, is_buy_hedge=True)

            for order_info in new_orders:
                active_hedge_orders['buy_hedge'].append({
                    'order_id': order_info['order_id'],
                    'original_qty': order_info['quantity'],
                    'remaining_qty': order_info['quantity'],
                    'price': order_info['price'],
                    'hedge_target_id': 2,  # 对冲的是id=2的卖单
                    'batch_index': order_info['batch_index']
                })
                # 记录订单创建时间
                order_creation_times[order_info['order_id']] = time.time()

            logging.info(f"为卖单汇总下了{len(new_orders)}笔买单对冲，总数量: {needed_hedge_qty}")






def check_hedge_order_execution():
    """
    检查对冲单的成交情况，每10-20秒调用一次
    """
    global active_hedge_orders

    for hedge_type, hedge_orders_list in active_hedge_orders.items():
        if not hedge_orders_list:  # 如果列表为空
            continue

        # 需要移除的订单索引
        orders_to_remove = []

        for i, hedge_info in enumerate(hedge_orders_list):
            order_id = hedge_info['order_id']
            original_qty = hedge_info['original_qty']
            remaining_qty = hedge_info['remaining_qty']
            hedge_target_id = hedge_info['hedge_target_id']

            # 获取订单状态
            status, filled_qty = get_order_status(order_id)

            if status == 'ERROR':
                continue

            # 如果有新的成交
            if status in ['FILLED_OR_CANCELED']:
                # 订单已完成，需要获取实际成交数量
                # 由于无法直接获取成交数量，假设完全成交
                if hedge_info['remaining_qty'] > 0:
                    new_executed_qty = hedge_info['remaining_qty']
                    filled_qty = original_qty

                    logging.info(f"对冲单 {order_id} 已完成，假设完全成交: {new_executed_qty}")

                    # 更新对冲单信息
                    hedge_info['remaining_qty'] = 0

                    # 获取当前聚合订单信息
                    current_orders = get_current_aggregated_orders()

                    if hedge_target_id == 1 and current_orders['buy_order']:
                        # 对冲买单，更新买单数量
                        current_qty = current_orders['buy_order']['eat_qty']
                        new_qty = max(0, current_qty - new_executed_qty)
                        update_aggregated_order_qty(1, new_qty)

                    elif hedge_target_id == 2 and current_orders['sell_order']:
                        # 对冲卖单，更新卖单数量
                        current_qty = current_orders['sell_order']['eat_qty']
                        new_qty = max(0, current_qty - new_executed_qty)
                        update_aggregated_order_qty(2, new_qty)
            else:
                # 检查是否有部分成交
                executed_qty = original_qty - remaining_qty
                new_executed_qty = filled_qty - executed_qty

                if new_executed_qty > 0:
                    logging.info(f"对冲单 {order_id} 新成交 {new_executed_qty}，总成交 {filled_qty}")

                    # 更新对冲单信息
                    hedge_info['remaining_qty'] = original_qty - filled_qty

                    # 获取当前聚合订单信息
                    current_orders = get_current_aggregated_orders()

                    if hedge_target_id == 1 and current_orders['buy_order']:
                        # 对冲买单，更新买单数量
                        current_qty = current_orders['buy_order']['eat_qty']
                        new_qty = max(0, current_qty - new_executed_qty)
                        update_aggregated_order_qty(1, new_qty)

                    elif hedge_target_id == 2 and current_orders['sell_order']:
                        # 对冲卖单，更新卖单数量
                        current_qty = current_orders['sell_order']['eat_qty']
                        new_qty = max(0, current_qty - new_executed_qty)
                        update_aggregated_order_qty(2, new_qty)

            # 如果订单完全成交或取消，标记为需要移除
            if status in ['FILLED', 'CANCELED', 'REJECTED', 'FILLED_OR_CANCELED'] or hedge_info['remaining_qty'] <= 0:
                logging.info(f"对冲单 {order_id} 状态: {status}，标记清理")
                orders_to_remove.append(i)

        # 从后往前移除已完成的订单，避免索引问题
        for i in reversed(orders_to_remove):
            removed_order = hedge_orders_list.pop(i)
            # 从创建时间记录中移除
            order_creation_times.pop(removed_order['order_id'], None)
            logging.info(f"已清理对冲单: {removed_order['order_id']}")





def update_hedge_orders_based_on_aggregation(aggregated_result):
    """
    根据新的聚合结果更新对冲单

    Parameters:
    -----------
    aggregated_result : dict
        最新的聚合结果
    """
    global active_hedge_orders

    # 获取当前数据库中的真实聚合数据
    current_orders = get_current_aggregated_orders()

    # 使用数据库中的真实数据，而不是聚合结果中的数据
    # 因为聚合结果只包含本次处理的交易，不包含数据库中的完整状态
    buy_order = current_orders.get('buy_order')
    sell_order = current_orders.get('sell_order')

    logging.info(
        f"检查对冲单状态 - 数据库买单: {buy_order['eat_qty'] if buy_order else 0}, 数据库卖单: {sell_order['eat_qty'] if sell_order else 0}")

    # 检查买单对冲是否需要调整
    if active_hedge_orders['sell_hedge']:  # 如果有卖单对冲列表
        if buy_order and buy_order['eat_qty'] > 0:
            # 计算当前所有对冲单的剩余数量
            total_hedge_qty = sum(hedge['remaining_qty'] for hedge in active_hedge_orders['sell_hedge'])
            expected_hedge_qty = buy_order['eat_qty']

            if abs(expected_hedge_qty - total_hedge_qty) > 0.01:  # 允许小误差
                logging.info(f"买单对冲数量不匹配: 期望{expected_hedge_qty}, 当前{total_hedge_qty}")

                # 如果差异较大，取消所有对冲单，重新下单
                if abs(expected_hedge_qty - total_hedge_qty) > expected_hedge_qty * 0.1:  # 超过10%差异
                    logging.info("差异较大，取消所有卖单对冲")
                    for hedge_info in active_hedge_orders['sell_hedge']:
                        cancel_order(hedge_info['order_id'])
                    active_hedge_orders['sell_hedge'] = []
        else:
            # 如果没有买单了，取消所有对冲
            logging.info("数据库中没有买单，取消所有卖单对冲")
            for hedge_info in active_hedge_orders['sell_hedge']:
                cancel_order(hedge_info['order_id'])
            active_hedge_orders['sell_hedge'] = []

    # 检查卖单对冲是否需要调整
    if active_hedge_orders['buy_hedge']:  # 如果有买单对冲列表
        if sell_order and sell_order['eat_qty'] > 0:
            # 计算当前所有对冲单的剩余数量
            total_hedge_qty = sum(hedge['remaining_qty'] for hedge in active_hedge_orders['buy_hedge'])
            expected_hedge_qty = sell_order['eat_qty']

            if abs(expected_hedge_qty - total_hedge_qty) > 0.01:  # 允许小误差
                logging.info(f"卖单对冲数量不匹配: 期望{expected_hedge_qty}, 当前{total_hedge_qty}")

                # 如果差异较大，取消所有对冲单，重新下单
                if abs(expected_hedge_qty - total_hedge_qty) > expected_hedge_qty * 0.1:  # 超过10%差异
                    logging.info("差异较大，取消所有买单对冲")
                    for hedge_info in active_hedge_orders['buy_hedge']:
                        cancel_order(hedge_info['order_id'])
                    active_hedge_orders['buy_hedge'] = []
        else:
            # 如果没有卖单了，取消所有对冲
            logging.info("数据库中没有卖单，取消所有买单对冲")
            for hedge_info in active_hedge_orders['buy_hedge']:
                cancel_order(hedge_info['order_id'])
            active_hedge_orders['buy_hedge'] = []





def hedge_monitoring_thread():
    """
    对冲单监控线程，每15秒检查一次成交情况
    """
    logging.info("对冲单监控线程启动")
    while True:
        try:
            check_hedge_order_execution()
            time.sleep(15)  # 每15秒检查一次
        except Exception as e:
            logging.error(f"对冲单监控线程发生错误: {e}")
            time.sleep(5)  # 发生错误时等待5秒



# 主程序
if __name__ == '__main__':
    # 启动对冲单监控线程
    monitoring_thread = threading.Thread(target=hedge_monitoring_thread, daemon=True)
    monitoring_thread.start()

    print('start program with hedge system')
    while True:
        try:
            # 0. 检查是否需要刷新挂单
            refresh_performed = perform_order_refresh()
            if refresh_performed:
                logging.info("已执行订单刷新，继续正常流程")

            # 1. 聚合交易数据
            results = aggregate_bybusdt()

            if results:
                # 检查是否有新交易数据
                if results.get('no_new_trades', False):
                    logging.info("没有新交易数据，但检查数据库中的聚合订单")

                    # 获取数据库中的聚合数据
                    current_orders = get_current_aggregated_orders()

                    if current_orders['buy_order'] or current_orders['sell_order']:
                        logging.info(
                            f"数据库中的聚合数据 - 买单: {current_orders['buy_order']['eat_qty'] if current_orders['buy_order'] else 0}, 卖单: {current_orders['sell_order']['eat_qty'] if current_orders['sell_order'] else 0}")

                        # 直接为数据库中的聚合数据创建对冲单，不依赖新交易
                        update_hedge_orders_based_on_aggregation(current_orders)
                        check_and_place_hedge_orders(current_orders)
                    else:
                        logging.info("数据库中也没有聚合数据")

                else:
                    # 有新交易数据的正常流程
                    logging.info(f"聚合结果: 处理了{results['processed_trades_count']}条交易")

                    # 打印聚合订单信息
                    if results['buy_order']:
                        buy = results['buy_order']
                        logging.info(f"买单汇总: {buy['eat_qty']} @ {buy['price']} (总价值: {buy['total_value']:.2f})")

                    if results['sell_order']:
                        sell = results['sell_order']
                        logging.info(
                            f"卖单汇总: {sell['eat_qty']} @ {sell['price']} (总价值: {sell['total_value']:.2f})")

                    # 2. 根据聚合结果更新对冲单
                    update_hedge_orders_based_on_aggregation(results)

                    # 3. 检查并下新的对冲单
                    check_and_place_hedge_orders(results)

                # 4. 显示当前对冲单状态
                logging.info(f"当前对冲单状态:")
                if active_hedge_orders['buy_hedge']:
                    total_buy_hedge = sum(hedge['remaining_qty'] for hedge in active_hedge_orders['buy_hedge'])
                    logging.info(f"  买单对冲: {len(active_hedge_orders['buy_hedge'])}笔，总数量: {total_buy_hedge}")
                    for i, hedge in enumerate(active_hedge_orders['buy_hedge']):
                        logging.info(
                            f"    #{i + 1}: {hedge['remaining_qty']} @ {hedge['price']} (订单ID: {hedge['order_id']})")
                else:
                    logging.info(f"  买单对冲: 无")

                if active_hedge_orders['sell_hedge']:
                    total_sell_hedge = sum(hedge['remaining_qty'] for hedge in active_hedge_orders['sell_hedge'])
                    logging.info(f"  卖单对冲: {len(active_hedge_orders['sell_hedge'])}笔，总数量: {total_sell_hedge}")
                    for i, hedge in enumerate(active_hedge_orders['sell_hedge']):
                        logging.info(
                            f"    #{i + 1}: {hedge['remaining_qty']} @ {hedge['price']} (订单ID: {hedge['order_id']})")
                else:
                    logging.info(f"  卖单对冲: 无")

            else:
                logging.info("没有找到未处理的交易数据")

                # 即使没有新数据，也要检查现有对冲单是否需要调整
                current_orders = get_current_aggregated_orders()
                update_hedge_orders_based_on_aggregation(current_orders)

            # 5. 等待下一次循环
            sleep_time = generate_random_sleep()
            logging.info(f"等待 {sleep_time:.1f} 秒后进行下一次循环")
            time.sleep(sleep_time)

        except Exception as e:
            logging.error(f"主循环发生错误: {e}")
            time.sleep(10)  # 发生错误时等待10秒






# if __name__ == '__main__':
#     # main(symbol, max_usdt)
#     print('start program')
#     while True:
#         results = aggregate_bybusdt()
#         sleep_time = generate_random_sleep()
#         time.sleep(sleep_time)
    
