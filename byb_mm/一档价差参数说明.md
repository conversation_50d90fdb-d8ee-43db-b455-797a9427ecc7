# 一档价差参数说明

## 概述
在 `byb_mm_new_simu.py` 中新增了一个可配置的参数 `first_level_spread_ratio`，用于控制买卖一档最优价差。

## 参数详情

### 参数名称
`first_level_spread_ratio`

### 参数位置
在 `BybMarketMaker` 类的 `__init__` 方法中，位于价差控制参数部分：

```python
# 价差控制参数
self.min_spread = 0.0002  # 最小价差
self.max_spread = 0.001  # 最大价差
self.first_level_spread_ratio = 0.06  # 买卖一档最优价差比例，初始值6%
```

### 默认值
- **初始值**: 0.06 (6%)
- **含义**: 一档买卖价差占中间价的比例

### 功能说明

1. **只控制一档价差**: 该参数仅影响买卖一档的价差，不影响其他档位的价差计算
2. **替代千二限制**: 原来的千二(0.2%)限制已被移除，现在使用此参数控制
3. **最小值保护**: 确保价差不小于1个tick size (0.0001)

### 计算逻辑

```python
# 计算一档价差值
first_level_spread_value = self.mid_price * self.first_level_spread_ratio

# 确保不小于最小tick size
min_spread = max(self.price_step, first_level_spread_value)
```

### 使用示例

假设中间价为 0.1234：

| 价差比例 | 价差值 | 买一价 | 卖一价 | 实际价差 |
|---------|--------|--------|--------|----------|
| 1% | 0.001234 | 0.122166 | 0.124634 | 0.002468 |
| 2% | 0.002468 | 0.120932 | 0.125868 | 0.004936 |
| 6% | 0.007404 | 0.115996 | 0.130804 | 0.014808 |
| 10% | 0.012340 | 0.111060 | 0.135740 | 0.024680 |

### 修改方法

要修改一档价差，只需在代码中调整 `first_level_spread_ratio` 的值：

```python
# 设置为3%价差
mm.first_level_spread_ratio = 0.03

# 设置为8%价差  
mm.first_level_spread_ratio = 0.08
```

### 日志输出

系统会在DEBUG级别输出一档价差的计算信息：

```
一档价差参数: 比例=6.0%, 价差值=0.007404, 最终价差=0.007404
```

### 注意事项

1. **参数范围**: 建议设置在 1%-15% 之间，过小可能导致频繁成交，过大可能影响流动性
2. **tick size限制**: 无论设置多小的比例，最终价差都不会小于1个tick size
3. **库存影响**: 库存偏移仍会影响一档价格，但有最大限制防止价差过大
4. **其他档位**: 第二档及以后的档位价差计算逻辑保持不变

## 测试验证

可以运行 `test_first_level_spread.py` 脚本来测试不同价差比例的效果。
