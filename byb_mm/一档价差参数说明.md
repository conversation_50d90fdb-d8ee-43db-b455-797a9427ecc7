# 价差控制参数说明

## 概述
在 `byb_mm_new_simu.py` 中新增了多个可配置的价差控制参数：
- `first_level_spread_ratio`：控制买卖一档最优价差
- `level_spread_base`：控制其他档位间的基础价差
- `level_spread_increment`：控制档位间价差的递增量

## 参数详情

### 一档价差参数（分离式）
**参数名称**: `first_level_bid_spread_ratio`
**默认值**: 0.03 (3%)
**含义**: 买一档价差占中间价的比例

**参数名称**: `first_level_ask_spread_ratio`
**默认值**: 0.03 (3%)
**含义**: 卖一档价差占中间价的比例

### 档位间价差参数
**参数名称**: `level_spread_base`
**默认值**: 0.002 (千二，0.2%)
**含义**: 其他档位间的基础价差比例

**参数名称**: `level_spread_increment`
**默认值**: 0.001 (千一，0.1%)
**含义**: 档位间价差的递增量

### 参数位置
在 `BybMarketMaker` 类的 `__init__` 方法中：

```python
# 价差控制参数
self.min_spread = 0.0002  # 最小价差
self.max_spread = 0.001  # 最大价差
self.first_level_bid_spread_ratio = 0.03  # 买一档价差比例，初始值3%
self.first_level_ask_spread_ratio = 0.03  # 卖一档价差比例，初始值3%
self.level_spread_base = 0.002  # 其他档位间基础价差比例，千二(0.2%)
self.level_spread_increment = 0.001  # 档位间价差递增量，千一(0.1%)
```

### 功能说明

#### 一档价差控制（分离式）
1. **分离控制**: `first_level_bid_spread_ratio` 和 `first_level_ask_spread_ratio` 分别控制买一档和卖一档的价差
2. **非对称支持**: 支持买卖两侧设置不同的价差比例
3. **替代千二限制**: 原来的千二(0.2%)限制已被移除
4. **买卖单交叉检测**: 当检测到买卖单价格交叉时，使用这两个参数分别调整价格
5. **价差过大保护**: 当一档价差超过配置比例时，自动调整到合理范围

#### 档位间价差控制
1. **渐进式价差**: 从第二档开始，使用 `level_spread_base` + `level_spread_increment` 计算档位间价差
2. **千二到百一**: 默认配置下，第二档千二(0.2%)，第三档千三(0.3%)，...，第十档百一(1.0%)
3. **灵活配置**: 可以通过调整基础价差和递增量来改变档位间价差的分布
4. **最小值保护**: 确保所有档位间价差不小于1个tick size (0.0001)

### 计算逻辑

#### 一档价差计算（分离式）
```python
# 分别计算买一档和卖一档的价差
bid_spread_value = self.mid_price * self.first_level_bid_spread_ratio
ask_spread_value = self.mid_price * self.first_level_ask_spread_ratio

# 确保不小于最小tick size
bid_spread_value = max(self.price_step, bid_spread_value)
ask_spread_value = max(self.price_step, ask_spread_value)

# 计算价格
bid_price = self.mid_price - bid_spread_value
ask_price = self.mid_price + ask_spread_value
```

#### 档位间价差计算
```python
# 计算第i档与前一档的价差 (i从2开始)
level_increment = self.level_spread_base + (i - 1) * self.level_spread_increment
level_spread_value = self.mid_price * level_increment
level_spread_value = max(self.price_step, level_spread_value)

# 从前一档价格计算当前档价格
current_bid = prev_bid - level_spread_value
current_ask = prev_ask + level_spread_value
```

### 使用示例

假设中间价为 0.1234：

#### 一档价差示例（分离式）
假设中间价为 0.1666：

**对称价差示例**：
| 买价差比例 | 卖价差比例 | 买一价 | 卖一价 | 总价差 |
|-----------|-----------|--------|--------|--------|
| 1.0% | 1.0% | 0.1649 | 0.1683 | 0.0034 |
| 2.0% | 2.0% | 0.1633 | 0.1699 | 0.0066 |
| 3.0% | 3.0% | 0.1616 | 0.1716 | 0.0100 |

**非对称价差示例**：
| 买价差比例 | 卖价差比例 | 买一价 | 卖一价 | 总价差 |
|-----------|-----------|--------|--------|--------|
| 2.0% | 4.0% | 0.1633 | 0.1733 | 0.0100 |
| 1.0% | 5.0% | 0.1649 | 0.1749 | 0.0100 |
| 4.0% | 2.0% | 0.1599 | 0.1699 | 0.0100 |

#### 档位间价差示例（默认配置）
| 档位 | 价差比例 | 价差值 | 累计价差 | 描述 |
|------|----------|--------|----------|------|
| 1档 | 6.0% | 0.007404 | 0.007404 | 一档价差 |
| 2档 | 0.2% | 0.000247 | 0.007651 | 千二 |
| 3档 | 0.3% | 0.000370 | 0.008021 | 千三 |
| 4档 | 0.4% | 0.000494 | 0.008515 | 千四 |
| 5档 | 0.5% | 0.000617 | 0.009132 | 千五 |
| ... | ... | ... | ... | ... |
| 10档 | 1.0% | 0.001234 | 0.014068 | 百一 |

### 修改方法

#### 修改一档价差（分离式）
```python
# 对称价差：买卖两侧都设置为3%
mm.first_level_bid_spread_ratio = 0.03
mm.first_level_ask_spread_ratio = 0.03

# 非对称价差：买侧2%，卖侧4%
mm.first_level_bid_spread_ratio = 0.02
mm.first_level_ask_spread_ratio = 0.04

# 保守买侧，激进卖侧
mm.first_level_bid_spread_ratio = 0.01
mm.first_level_ask_spread_ratio = 0.05
```

#### 修改档位间价差
```python
# 保守型配置：千一基础 + 0.5千一递增
mm.level_spread_base = 0.001      # 千一(0.1%)
mm.level_spread_increment = 0.0005 # 0.5千一(0.05%)

# 激进型配置：千三基础 + 1.5千一递增
mm.level_spread_base = 0.003      # 千三(0.3%)
mm.level_spread_increment = 0.0015 # 1.5千一(0.15%)
```

### 日志输出

系统会在DEBUG级别输出价差计算信息：

#### 一档价差日志（分离式）
```
一档价差参数: 买价差比例=3.0%, 卖价差比例=3.0%, 买价差值=0.004998, 卖价差值=0.004998, 总价差=0.009996
第一档价格: bid=0.1616, ask=0.1716, 买价差=0.005000(3.0%), 卖价差=0.005000(3.0%), 总价差=0.010000
```

#### 档位间价差日志
```
档位间价差参数: 基础比例=0.2%, 递增量=0.1%
前5档价差: ['0.007404', '0.007651', '0.008021', '0.008515', '0.009132']
第2档价差: 配置比例=0.2%, 价差值=0.000247
第3档价差: 配置比例=0.3%, 价差值=0.000370, 实际买价差=0.000400, 实际卖价差=0.000400
```

### 注意事项

#### 一档价差参数（分离式）
1. **参数范围**: 建议每侧设置在 0.5%-10% 之间，过小可能导致频繁成交，过大可能影响流动性
2. **tick size限制**: 无论设置多小的比例，最终价差都不会小于1个tick size
3. **库存影响**: 库存偏移仍会影响一档价格，但有最大限制防止价差过大
4. **非对称策略**: 可以根据市场方向预期设置不同的买卖价差，如看涨时设置较小的买价差和较大的卖价差

#### 档位间价差参数
1. **基础价差**: 建议设置在 0.1%-0.5% 之间，过小可能导致档位间价差不明显
2. **递增量**: 建议设置在 0.05%-0.2% 之间，过大可能导致远档位价差过大
3. **最大档位**: 当价差达到百一(1%)时，建议不再继续增加
4. **价格单调性**: 系统会自动确保买单价格单调递减，卖单价格单调递增

## 买卖单交叉检测优化

### 优化内容

1. **价格交叉检测**: 当买单最高价 >= 卖单最低价时，系统会检测到价格交叉
2. **自动调整机制**: 使用 `first_level_spread_ratio` 参数计算合理的价差并调整价格
3. **价差过大保护**: 当一档价差超过配置比例时，自动缩小到合理范围

### 调整逻辑

当检测到价格交叉或价差过大时：

```python
# 计算中间价
mid_price = (best_bid + best_ask) / 2

# 计算目标价差
target_spread = mid_price * self.first_level_spread_ratio
min_spread = max(self.price_step, target_spread)  # 确保不小于1个tick

# 调整价格
new_best_bid = mid_price - min_spread / 2
new_best_ask = mid_price + min_spread / 2
```

### 应用场景

1. **报价生成时**: 在生成分层报价时检查和调整一档价差
2. **价格交叉时**: 当买卖价格出现交叉时自动调整
3. **价差验证时**: 在最终验证阶段确保价差在合理范围内
4. **显示调整时**: 在显示报价前进行最后的价差调整

### 日志输出

系统会输出相关的调整信息：

```
检测到买卖单价格交叉: 买价=0.124000, 卖价=0.123000
价格交叉调整: 使用价差比例=6.0%, 调整后买价=0.119800, 卖价=0.127200, 价差=0.007400
一档价差 0.008000 超过配置限制 0.007404，进行调整
```

## 测试验证

系统已通过多种场景测试：

### 一档价差测试
- 正常情况无交叉
- 价格交叉自动调整
- 价差过大自动缩小
- 不同价差比例的效果验证

### 档位间价差测试
- 千二到百一的渐进式价差
- 不同参数配置的效果对比
- 价格单调性验证
- 最小tick size保护验证

### 综合测试
- 一档价差与档位间价差的协调工作
- 库存偏移对各档位价格的影响
- 极端市场条件下的价差控制
