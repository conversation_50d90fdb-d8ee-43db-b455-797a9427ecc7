# calculate_exposure.py 优化总结

## 优化目标
1. **减少挂单数据到10%** - 避免对盘口造成过度阻塞
2. **增加订单分散度** - 减少订单密集度，提高价格间距

## 主要优化内容

### 1. 配置参数优化 (HEDGE_CONFIG)

#### 新增参数
- `volume_reduction_factor: 0.1` - 挂单量减少到10%

#### 修改参数
- `max_batches: 12 → 8` - 减少最大批次数量，降低订单密集度
- `price_spread_ratio: 0.002 → 0.008` - 价格分散范围从千二增加到千八
- `quantity_variation: 0.3 → 0.5` - 增加数量变异系数，提高随机性
- `time_interval_range: (0.05, 0.3) → (0.2, 0.8)` - 增加时间间隔，减少密集度
- `micro_adjustment_range: 0.00002 → 0.00008` - 增加价格微调范围

### 2. 对冲数量计算优化

#### 买单对冲逻辑
```python
# 原逻辑：needed_hedge_qty = buy_order['eat_qty'] - current_hedge_qty
# 优化后：
target_hedge_qty = buy_order['eat_qty'] * HEDGE_CONFIG['volume_reduction_factor']
needed_hedge_qty = target_hedge_qty - current_hedge_qty
```

#### 卖单对冲逻辑
```python
# 原逻辑：needed_hedge_qty = sell_order['eat_qty'] - current_hedge_qty  
# 优化后：
target_hedge_qty = sell_order['eat_qty'] * HEDGE_CONFIG['volume_reduction_factor']
needed_hedge_qty = target_hedge_qty - current_hedge_qty
```

### 3. 价格分散优化

#### 价格范围扩大
```python
# 原逻辑：固定的1.5‰和0.5‰价差
# 优化后：使用配置的千八价差，动态计算
spread_factor = HEDGE_CONFIG['price_spread_ratio']  # 0.008
if is_buy_hedge:
    price_range_start = base_hedge_price * (1 - spread_factor * 0.8)  # 下方约6.4‰
    price_range_end = base_hedge_price * (1 + spread_factor * 0.2)    # 上方约1.6‰
else:
    price_range_start = base_hedge_price * (1 - spread_factor * 0.2)  # 下方约1.6‰
    price_range_end = base_hedge_price * (1 + spread_factor * 0.8)    # 上方约6.4‰
```

### 4. 批次计算优化

#### 减少批次数量
```python
# 原逻辑：max_batches = min(min_batches + 3, 12)
# 优化后：
max_batches = min(min_batches + 2, HEDGE_CONFIG['max_batches'])  # 最多8批
```

### 5. 时间间隔优化

#### 成功下单间隔
```python
# 原逻辑：success_interval = random.uniform(0.1, 0.4)
# 优化后：
success_interval = random.uniform(*HEDGE_CONFIG['time_interval_range'])  # 0.2-0.8秒
```

#### 失败重试间隔
```python
# 原逻辑：failure_interval = random.uniform(0.5, 1.0)
# 优化后：
failure_interval = random.uniform(1.0, 2.0)  # 1-2秒
```

### 6. 价格随机性增强

#### 微调范围扩大
```python
# 原逻辑：micro_variation = random.uniform(-0.00005, 0.00005)
# 优化后：
micro_variation = random.uniform(-HEDGE_CONFIG['micro_adjustment_range'], 
                                HEDGE_CONFIG['micro_adjustment_range'])  # ±0.00008
```

## 优化效果

### 数量方面
- **对冲挂单量减少90%** - 从100%减少到10%
- **批次数量减少** - 最大批次从12减少到8
- **订单密集度显著降低**

### 价格分散方面
- **价格分散范围扩大4倍** - 从千二增加到千八
- **价格随机性增强4倍** - 微调范围从±0.00002增加到±0.00008
- **订单价格间距显著增加**

### 时间分散方面
- **下单间隔增加** - 成功间隔从0.1-0.4秒增加到0.2-0.8秒
- **失败重试间隔加倍** - 从0.5-1.0秒增加到1.0-2.0秒
- **整体下单频率降低**

## 预期效果
1. **减少盘口阻塞** - 挂单量减少90%，显著降低对市场的影响
2. **提高价格分散** - 订单分布更广，避免价格密集堆积
3. **降低检测风险** - 更自然的订单分布和时间间隔
4. **保持对冲效果** - 虽然数量减少，但仍能有效对冲风险

## 新增功能：定期刷新挂单

### 功能概述
添加了定期刷新挂单功能，每隔几小时自动取消现有订单并根据当前盘口价格重新挂单。

### 配置参数
```python
'refresh_interval_hours': 3,  # 每3小时刷新一次挂单
'refresh_price_deviation_threshold': 0.005,  # 价格偏离超过千五时立即刷新
'max_order_age_hours': 6,  # 订单最大存活时间（小时）
```

### 刷新触发条件
1. **定期刷新**：距离上次刷新超过3小时
2. **价格偏离刷新**：订单价格与市场价格偏离超过千五(0.5%)
3. **订单过期刷新**：订单存在时间超过6小时

### 刷新流程
1. **检查刷新条件** - `should_refresh_orders()`
   - 检查时间间隔
   - 检查价格偏离程度
   - 检查订单年龄

2. **取消所有订单** - `cancel_all_hedge_orders()`
   - 取消所有买单对冲
   - 取消所有卖单对冲
   - 清理订单记录

3. **重新创建订单** - `refresh_hedge_orders_based_on_market()`
   - 获取当前市场价格
   - 基于最新价格重新计算对冲价格
   - 创建新的对冲订单

### 核心函数

#### `should_refresh_orders() -> tuple[bool, str]`
检查是否需要刷新挂单，返回是否需要刷新和原因。

#### `cancel_all_hedge_orders(reason: str)`
取消所有对冲单，清理相关记录。

#### `refresh_hedge_orders_based_on_market()`
基于当前市场价格重新创建对冲单。

#### `perform_order_refresh() -> bool`
执行完整的订单刷新流程。

### 订单生命周期管理
- **创建时记录** - 每个订单创建时记录时间戳
- **成交时清理** - 订单成交或取消时清理时间记录
- **定期检查** - 主循环中定期检查是否需要刷新

### 日志记录
- 刷新触发原因详细记录
- 取消订单数量统计
- 重新创建订单信息
- 价格偏离情况监控

### 优势
1. **适应市场变化** - 定期根据最新市场价格调整订单
2. **避免订单老化** - 防止订单价格过时
3. **降低检测风险** - 定期更换订单，模拟真实交易行为
4. **灵活配置** - 可调整刷新间隔和触发条件

## 注意事项
1. 需要监控对冲效果，确保风险控制仍然有效
2. 可根据实际运行情况微调 `volume_reduction_factor` 参数
3. 建议在测试环境先验证优化效果
4. **新增**：监控刷新频率，避免过于频繁的刷新影响交易
5. **新增**：关注刷新时的网络延迟和API限制
6. **新增**：定期检查刷新逻辑是否正常工作
